<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.5.3</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.shadysentry.spring</groupId>
	<artifactId>gRPC-demo-server</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>gRPC-demo-server</name>
	<description>gRPC demo server for Spring Boo project</description>

	<properties>
		<java.version>17</java.version>
		<grpc.version>1.72.0</grpc.version>
		<protobuf-java.version>4.30.2</protobuf-java.version>
		<spring-grpc.version>0.9.0</spring-grpc.version>
		<org.mapstruct.version>1.6.3</org.mapstruct.version>
	</properties>
	<dependencies>
		<dependency>
			<groupId>io.grpc</groupId>
			<artifactId>grpc-services</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.grpc</groupId>
			<artifactId>spring-grpc-spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.grpc</groupId>
			<artifactId>spring-grpc-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct</artifactId>
			<version>${org.mapstruct.version}</version>
		</dependency>

	</dependencies>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.grpc</groupId>
				<artifactId>spring-grpc-dependencies</artifactId>
				<version>${spring-grpc.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>io.github.ascopes</groupId>
				<artifactId>protobuf-maven-plugin</artifactId>
				<version>3.4.2</version>
				<configuration>
					<protocVersion>${protobuf-java.version}</protocVersion>
					<binaryMavenPlugins>
						<binaryMavenPlugin>
							<groupId>io.grpc</groupId>
							<artifactId>protoc-gen-grpc-java</artifactId>
							<version>${grpc.version}</version>
							<options>@generated=omit</options>
						</binaryMavenPlugin>
					</binaryMavenPlugins>
				</configuration>
				<executions>
					<execution>
						<id>generate</id>
						<goals>
							<goal>generate</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

</project>
