package com.shadysentry.spring.gRPC_demo_server.service;

import io.grpc.stub.StreamObserver;
import org.springframework.boot.grpc.GreeterGrpc;
import org.springframework.boot.grpc.HelloWorldProto;
import org.springframework.grpc.server.service.GrpcService;

@GrpcService
public class HelloWorldService extends GreeterGrpc.GreeterImplBase {
    @Override
    public void sayHello(HelloWorldProto.HelloRequest request,
                         StreamObserver<HelloWorldProto.HelloReply> responseObserver) {
        String responseMessage = String.format("Hello, dear %s %s!", request.getName(), request.getSurname());
        HelloWorldProto.HelloReply reply = HelloWorldProto.HelloReply.newBuilder()
                .setMessage(responseMessage).build();
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }
}
