syntax ="proto3";

option java_package="org.springframework.boot.grpc";
option java_outer_classname="HelloWorldProto";
// The greeting service definition.
service Greeter {
  // Sends a greeting
  rpc Say<PERSON><PERSON> (HelloRequest) returns (HelloReply) {}
}

// The request message containing the user's name.
message HelloRequest {
  string name = 1;
  string surname = 2;
}

// The response message containing the greetings
message HelloReply {
  string message = 1;
}