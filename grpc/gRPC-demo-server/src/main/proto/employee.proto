syntax = "proto3";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

option java_package = "org.springframework.boot.grpc";
option java_multiple_files = true;

service EmployeeService{
  rpc getEmployeeById(EmployeeRequest) returns (Employee);

  rpc addEmployee(Employee)  returns (Employee);

  rpc getAllEmployees(google.protobuf.Empty) returns (EmployeeList);
}

message EmployeeRequest{
  int32 id = 1;
}
message EmployeeList{
  repeated Employee employees = 1;
}

message Employee{
  int32 id = 1;
  string name = 2;
  double salary = 3;
  repeated Department departments = 4;
  map<string, string> addressMap = 5;
  bool isActive = 6;
  bytes profilePicture = 7;
  google.protobuf.Timestamp joinDate = 8;
}

message Department{
  int32 id = 1;
  string name = 2;
}