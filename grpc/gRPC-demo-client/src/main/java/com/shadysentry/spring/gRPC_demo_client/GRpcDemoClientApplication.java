package com.shadysentry.spring.gRPC_demo_client;

import com.shadysentry.spring.gRPC_demo_client.service.GrpcClientService;
import com.shadysentry.spring.gRPC_demo_client.service.Task;
import com.shadysentry.spring.gRPC_demo_client.service.TaskDescription;
import com.shadysentry.spring.gRPC_demo_client.service.TaskExecutionResult;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.UUID;

@SpringBootApplication
@EnableScheduling
public class GRpcDemoClientApplication {

    public static void main(String[] args) {
        ApplicationContext context = SpringApplication.run(GRpcDemoClientApplication.class, args);

//        Task task = (Task) context.getBean("task");
//        task.setContext(new TaskDescription(UUID.randomUUID(), "test_Task",
//                1,
//                99,
//                1,
//                1));
//
//        TaskExecutionResult taskExecutionResult = task.calculate();
//        System.out.println(taskExecutionResult.toString());
        GrpcClientService service = context.getBean(GrpcClientService.class);

        for (int i = 0; i < 10; i++) {
            System.out.println(service.sayHello("BlaBla", "Gonzales"));
        }
    }

}
