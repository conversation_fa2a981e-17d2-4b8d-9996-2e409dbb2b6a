package com.shadysentry.spring.gRPC_demo_client.service;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Random;
import java.util.UUID;

@Component
@Scope("TaskScope")
public class Task {
    private TaskDescription context;
    private final UUID taskId;

    public Task() {
        taskId = UUID.randomUUID();
    }

    public void setContext(TaskDescription description) {
        this.context = description;
    }

    public TaskExecutionResult calculate() {
        Random random = new Random();
        var calculatedResult = random.nextInt(context.finalValue() - context.accuracy(),
                context.finalValue() + context.accuracy());
        return new TaskExecutionResult(taskId,
                UUID.randomUUID(), calculatedResult,
                Math.abs(context.finalValue() - calculatedResult));
    }
}
