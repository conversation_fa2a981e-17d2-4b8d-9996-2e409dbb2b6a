package com.shadysentry.spring.gRPC_demo_client.config;

import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.config.Scope;

import java.util.HashMap;
import java.util.Map;

public class TaskScope implements Scope {
    private Map<String, Object> beans = new HashMap<>();


    @Override
    public Object get(String name, ObjectFactory<?> objectFactory) {
//        Object bean = beans.get(name);
//        if (bean == null) {
        Object bean = objectFactory.getObject();
        beans.put(name, bean);
//        }
        return bean;
    }

    @Override
    public Object remove(String name) {
        return beans.remove(name);
    }

    @Override
    public void registerDestructionCallback(String name, Runnable callback) {

    }

    @Override
    public Object resolveContextualObject(String key) {
        return null;
    }

    @Override
    public String getConversationId() {
        return "";
    }
}
