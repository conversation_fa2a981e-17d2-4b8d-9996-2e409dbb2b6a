package com.shadysentry.spring.gRPC_demo_client.service;

import net.devh.boot.grpc.client.inject.GrpcClient;
import org.springframework.boot.grpc.GreeterGrpc;
import org.springframework.boot.grpc.HelloWorldProto;
import org.springframework.stereotype.Service;

@Service
public class GrpcClientService {

    @GrpcClient("gRPCService")
    private GreeterGrpc.GreeterBlockingStub greeterBlockingStub;

    public String sayHello(String name, String surname) {
        HelloWorldProto.HelloRequest request = HelloWorldProto.HelloRequest.newBuilder()
                .setName(name)
                .setSurname(surname)
                .build();

        HelloWorldProto.HelloReply reply = greeterBlockingStub.sayHello(request);

        return reply.getMessage();
    }

}
