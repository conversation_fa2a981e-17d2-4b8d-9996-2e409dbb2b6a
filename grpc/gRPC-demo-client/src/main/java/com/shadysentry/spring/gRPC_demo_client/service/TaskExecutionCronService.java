package com.shadysentry.spring.gRPC_demo_client.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Service
public class TaskExecutionCronService {
    Logger logger = LoggerFactory.getLogger(TaskExecutionCronService.class.getName());

    private Task task;

    public TaskExecutionCronService(Task task) {
        this.task = task;
    }

    @Scheduled(fixedRate = 1, timeUnit = TimeUnit.SECONDS)
    public void execute() {
        task.setContext(new TaskDescription("test_Task",
                1,
                99,
                1,
                1));

        TaskExecutionResult taskExecutionResult = task.calculate();
        System.out.println(taskExecutionResult.toString());
        logger.info("TaskExecutionResult={}", taskExecutionResult);
    }
}
