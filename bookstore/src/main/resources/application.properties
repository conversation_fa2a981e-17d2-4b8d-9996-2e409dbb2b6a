spring.application.name=bookstore
server.port=8080
#setup setting to connect to mongodb
spring.data.mongodb.uri=mongodb://localhost:27017/bookstore
spring.data.mongodb.auto-index-creation=true

logging.level.org.springframework.security=DEBUG
logging.level.org.shadysentry=DEBUG

api.jwt-settings.secret-key=9a8b7c6d5e4f3g2h1i0j9k8l7m6n5o4p3q2r1s0t
api.jwt-settings.expiration=15
api.common-path=/api
api.api-version=/v1

