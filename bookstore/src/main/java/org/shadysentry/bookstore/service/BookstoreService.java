package org.shadysentry.bookstore.service;

import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import jakarta.validation.Valid;
import org.bson.types.ObjectId;
import org.shadysentry.bookstore.data.Book;
import org.shadysentry.bookstore.data.BookDto;
import org.shadysentry.bookstore.data.BookMapper;
import org.shadysentry.bookstore.repository.BooksRepository;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class BookstoreService {
    private final BooksRepository booksRepository;
    private final BookMapper bookMapper;
    private final MongoTemplate mongoTemplate;

    public BookstoreService(BooksRepository booksRepository, BookMapper bookMapper, MongoTemplate mongoTemplate) {
        this.booksRepository = booksRepository;
        this.bookMapper = bookMapper;
        this.mongoTemplate = mongoTemplate;
    }

    public List<BookDto> getAllBooks() {
        Query query = new Query().with(Sort.by(Sort.Direction.ASC, "author"));

        var books = mongoTemplate.find(query, Book.class);

        return books.stream()
                .map(bookMapper::map)
                .collect(Collectors.toList());
    }

    public BookDto findById(ObjectId id) {
        var book = mongoTemplate.findById(id, Book.class);
        return bookMapper.map(book);
    }

    public BookDto insertOne(BookDto bookDto) {

        Book book = bookMapper.map(bookDto);
        Book savedBook = mongoTemplate.insert(book);

        return bookMapper.map(savedBook);
    }

    public DeleteResult deleteById(String id) {
        Query findById = new Query().addCriteria(Criteria.where("_id").is(new ObjectId(id)));

        DeleteResult deleteResult = mongoTemplate.remove(findById, Book.class);
        return deleteResult;
    }

    public UpdateResult updateById(String id, @Valid Map<String, String> bookDto) {
        Query findById = new Query().addCriteria(Criteria.where("_id").is(new ObjectId(id)));
        Update update = new Update();

        bookDto.forEach(update::set);

        UpdateResult updateResult = mongoTemplate.updateFirst(findById, update, Book.class);

        return updateResult;
    }

    public List<BookDto> getAllBooksPaged(int currentPage, int booksPerPage) {
        PageRequest pageRequest = PageRequest.of(currentPage, booksPerPage);
        Query getAll = new Query().with(Sort.by(Sort.Direction.ASC, "author")).with(pageRequest);

        List<Book> books = mongoTemplate.find(getAll, Book.class);
        return books.stream().map(bookMapper::map).collect(Collectors.toList());
    }
}
