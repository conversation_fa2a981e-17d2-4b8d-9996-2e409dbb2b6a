package org.shadysentry.bookstore.controller;

import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.bson.types.ObjectId;
import org.shadysentry.bookstore.data.BookDto;
import org.shadysentry.bookstore.error.InvalidIdException;
import org.shadysentry.bookstore.service.BookstoreService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("${api.common-path}${api.api-version}/books")
public class BooksController {
    private final BookstoreService bookstoreService;

    public BooksController(BookstoreService bookstoreService) {
        this.bookstoreService = bookstoreService;
    }

    @GetMapping(produces = "application/json")
    public ResponseEntity<List<BookDto>> getBooks(ServletRequest servletRequest) {
        var books = bookstoreService.getAllBooks();

        return new ResponseEntity<>(books, HttpStatus.OK);
    }

    @GetMapping
    public ResponseEntity<List<BookDto>> getBooksPaged(@RequestParam(name = "per_page", required = false, defaultValue = "20") int perPage,
                                                       @RequestParam(name = "current_page", required = false, defaultValue = "0") int currentPage) {
        var books = bookstoreService.getAllBooksPaged(currentPage, perPage);

        return new ResponseEntity<>(books, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    public ResponseEntity<BookDto> getBook(@PathVariable String id,
                                           ServletRequest servletRequest) throws InvalidIdException {
        if (!ObjectId.isValid(id)) {
            throw new InvalidIdException(id, "invalid object id cant be processed", ((HttpServletRequest) servletRequest).getRequestURI());
        }
        ObjectId objectId = new ObjectId(id);

        var books = bookstoreService.findById(objectId);
        HttpStatus status = books == null ? HttpStatus.NOT_FOUND : HttpStatus.OK;

        return new ResponseEntity<>(books, status);
    }


    @PostMapping()
    public ResponseEntity<BookDto> postBook(@Valid @RequestBody BookDto bookDto,
                                            ServletRequest servletRequest) {
        BookDto createdBook = bookstoreService.insertOne(bookDto);

        return new ResponseEntity<>(createdBook, HttpStatus.CREATED);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity deleteBookById(@PathVariable String id, ServletRequest servletRequest) throws InvalidIdException {
        if (!ObjectId.isValid(id)) {
            throw new InvalidIdException(id, "invalid object id cant be processed", ((HttpServletRequest) servletRequest).getRequestURI());
        }

        DeleteResult deletedResult = bookstoreService.deleteById(id);

        return new ResponseEntity<>(new deleteInfo(deletedResult.getDeletedCount(), deletedResult.wasAcknowledged()), HttpStatus.ACCEPTED);
    }

    //    @PatchMapping("/{id}")
//    public ResponseEntity UpdateBookById(@PathVariable String id,
//                                         @Valid @RequestBody Map<String, String> fields, ServletRequest servletRequest) throws InvalidIdException {
//        if (!ObjectId.isValid(id)) {
//            throw new InvalidIdException(id, "invalid object id cant be processed", ((HttpServletRequest) servletRequest).getRequestURI());
//        }
//
//        UpdateResult updateResult = bookstoreService.updateById(id, fields);
//
//        return new ResponseEntity<>(updateResult, HttpStatus.CREATED);
//    }
    @PatchMapping("/{id}")
    @ResponseBody
    public UpdateResult UpdateBookById(@PathVariable String id,
                                         @Valid @RequestBody Map<String, String> fields, ServletRequest servletRequest) throws InvalidIdException {
        if (!ObjectId.isValid(id)) {
            throw new InvalidIdException(id, "invalid object id cant be processed", ((HttpServletRequest) servletRequest).getRequestURI());
        }

        UpdateResult updateResult = bookstoreService.updateById(id, fields);
        return updateResult;

//        return new ResponseEntity<>(updateResult, HttpStatus.CREATED);
    }


    private record deleteInfo(long deletedCount, boolean acknowledged) {

    }
}
