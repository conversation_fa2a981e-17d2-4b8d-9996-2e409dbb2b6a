package org.shadysentry.bookstore.data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class BookDto {

    private String id;

    @NotEmpty
    private String title;
    @NotEmpty
    private String author;
    @Min(value = 1)
    private int pages;

    private List<String> genres = new ArrayList<>();
    @Positive
    private int rating;
}
