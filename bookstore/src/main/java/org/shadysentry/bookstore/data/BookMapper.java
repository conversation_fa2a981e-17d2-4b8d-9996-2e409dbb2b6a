package org.shadysentry.bookstore.data;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface BookMapper {
    @Mapping(target = "id",
            expression = "java(book.getId().toString())")
    BookDto map(Book book);

    @Mapping(target = "id",
            expression = "java(bookDto.getId()==null?null: new org.bson.types.ObjectId(bookDto.getId()))")
    Book map(BookDto bookDto);
}
