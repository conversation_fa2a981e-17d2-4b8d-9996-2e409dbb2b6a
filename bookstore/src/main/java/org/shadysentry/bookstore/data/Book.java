package org.shadysentry.bookstore.data;

import lombok.*;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.List;

@Getter
@Setter
@RequiredArgsConstructor
@Document(collection = "books")
public class Book {
    @Id
    private ObjectId id;
    @Field
    private String title;
    @Field
    private String author;
    @Field
    private int pages;
    @Field
    private List<String> genres;
    @Field
    private int rating;
}
