package org.shadysentry.bookstore.error;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

@ControllerAdvice
public class ErrorHandler {

    @ExceptionHandler(value = InvalidIdException.class)
    public ResponseEntity<ErrorResponse> processBadRequest(final InvalidIdException error) {
        return new ResponseEntity<>(new ErrorResponse(error.getId(), error.getMessage(), error.getSource())
                , HttpStatus.BAD_REQUEST);
    }
}
