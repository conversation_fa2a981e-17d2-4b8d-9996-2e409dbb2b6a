package org.shadysentry.bookstore.error;

import lombok.Getter;

@Getter
public class InvalidIdException extends Exception {
    public static final String NOT_SPECIFIED = "not specified";
    private final String id;
    private final String message;
    private final String source;

    public InvalidIdException(String message) {
        super(message);
        id = NOT_SPECIFIED;
        this.message = NOT_SPECIFIED;
        source = "undefined";
    }

    public InvalidIdException(String id, String message, String source) {
        super(String.format("Invalid Id: %s at %s", id, source));
        this.id = id;
        this.message = message;
        this.source = source;
    }
}
