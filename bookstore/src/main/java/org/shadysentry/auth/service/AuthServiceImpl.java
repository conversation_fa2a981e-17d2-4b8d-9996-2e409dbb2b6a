package org.shadysentry.auth.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.shadysentry.auth.model.*;
import org.shadysentry.auth.repository.UserRepository;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;


@Service
@Slf4j
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService, UserDetailsService {

    private final UserRepository userRepository;
    private final TokenService tokenService;
    private final PasswordEncoder passwordEncoder;
    private final AuthenticationConfiguration authenticationConfiguration;
    private final AuthMapper authMapper;

    @Override
    public void createUser(final CreateUserDto createUserDto) {
        try {
            final User createUser = authMapper.fromDto(createUserDto);
            String rawPassword = createUser.getPassword();
            String encodedPassword = passwordEncoder.encode(rawPassword);
            log.info("Raw password: {}, Encoded password: {}", rawPassword, encodedPassword);

            createUser.setPassword(encodedPassword);
            createUser.setRole(Role.END_USER);
            User createdUser = userRepository.save(createUser);
            log.info("created user: " + createdUser);
        } catch (Exception e) {
            log.error("[USER] : Error while trying to create user", e);
        }
    }

    @Override
    public User getUser(ObjectId id) {
        return userRepository.findById(id)
                .orElseThrow(() -> new UsernameNotFoundException("User not found"));
    }

    @Override
    public String login(LoginRequestDTO loginRequest) {
        try {
            AuthenticationManager authenticationManager = authenticationConfiguration.getAuthenticationManager();
            Authentication authRequest = AuthMapper.fromDto(loginRequest);

            Authentication authentication = authenticationManager.authenticate(authRequest);

            return tokenService.generateToken(authentication);
        } catch (UsernameNotFoundException e) {
            log.error("User not found: {}", loginRequest.email(), e);
            throw new BadCredentialsException("Invalid username or password");
        } catch (BadCredentialsException e) {
            log.error("[USER] : Bad credentials for user: {}", loginRequest.email(), e);
            throw e;
        } catch (Exception e) {
            log.error("[USER] : Error while trying to login user: {}", loginRequest.email(), e);
            throw new BadCredentialsException("Error while trying to login");
        }
    }


    @Override
    public boolean validateToken(String token) {
        return tokenService.validateToken(token);
    }

    @Override
    public String getUserFromToken(String token) {
        return tokenService.getUserFromToken(token);
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        log.info("[USER] : Loading user by email {}", username);
        return userRepository.findByEmail(username)
                .orElseThrow(() -> new UsernameNotFoundException("User not found"));
    }
}