package org.shadysentry.auth.service;

import org.bson.types.ObjectId;
import org.shadysentry.auth.model.CreateUserDto;
import org.shadysentry.auth.model.LoginRequestDTO;
import org.shadysentry.auth.model.User;

public interface AuthService {
    String login(LoginRequestDTO loginRequestDTO);

    boolean validateToken(String token);

    String getUserFromToken(String token);

    void createUser(CreateUserDto createUserDto);

    User getUser(ObjectId id);
}