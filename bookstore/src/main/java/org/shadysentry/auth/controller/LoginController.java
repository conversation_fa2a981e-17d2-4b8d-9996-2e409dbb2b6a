package org.shadysentry.auth.controller;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.shadysentry.auth.configuration.AuthConstants;
import org.shadysentry.auth.model.CreateUserDto;
import org.shadysentry.auth.model.LoginRequestDTO;
import org.shadysentry.auth.service.AuthService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;

@RestController
@RequestMapping("${api.common-path}${api.api-version}/auth")
@RequiredArgsConstructor
@Slf4j
public class LoginController {
    private final AuthService authService;

    @PostMapping("/register")
    public void createUser(@RequestBody CreateUserDto createUserDto) {
        authService.createUser(createUserDto);
    }

    @PostMapping("/login")
    public ResponseEntity<?> login(@RequestBody LoginRequestDTO loginRequestDTO,
                                HttpServletResponse response) {
        try {
            log.debug("Login attempt for user: {}", loginRequestDTO.email());
            String token = authService.login(loginRequestDTO);
            Cookie cookie = createAuthCookie(token);
            response.addCookie(cookie);
            
            log.info("User {} logged in successfully", loginRequestDTO.email());
            return ResponseEntity.ok().build();
            
        } catch (BadCredentialsException e) {
            log.warn("Failed login attempt for user: {}", loginRequestDTO.email());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Collections.singletonMap("error", "Invalid username or password"));
        } catch (Exception e) {
            log.error("Error during login for user: {}", loginRequestDTO.email(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Collections.singletonMap("error", "An error occurred during login"));
        }
    }

    @PostMapping("/logout")
    public void logout(HttpServletResponse response) {
        Cookie cookie = new Cookie(AuthConstants.TOKEN_COOKIE_NAME, StringUtils.EMPTY);
        cookie.setMaxAge(0);
        response.addCookie(cookie);
    }


    private Cookie createAuthCookie(String token) {
        String SAME_SITE_KEY = "SameSite";
        Cookie cookie = new Cookie(AuthConstants.TOKEN_COOKIE_NAME, token);

        cookie.setHttpOnly(AuthConstants.HTTP_ONLY);
        cookie.setSecure(AuthConstants.COOKIE_SECURE);
        cookie.setMaxAge(AuthConstants.COOKIE_MAX_AGE);
        cookie.setAttribute(SAME_SITE_KEY,AuthConstants.SAME_SITE);

        return cookie;
    }

}
