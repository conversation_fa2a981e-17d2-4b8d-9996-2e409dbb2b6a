package org.shadysentry.auth.configuration;

import org.shadysentry.AuthProperties;
import org.springframework.stereotype.Component;

@Component
public class ApiConfig {
    private static String COMMON_PATH;
    private static String API_VERSION;
    static String API_BASE_PATH;

    public ApiConfig(AuthProperties properties) {
        ApiConfig.COMMON_PATH = properties.getCommonPath();
        ApiConfig.API_VERSION = properties.getApiVersion();
        API_BASE_PATH = COMMON_PATH + API_VERSION;
    }

    public static String getApiBasePath() {
        return ApiConfig.API_BASE_PATH;
    }
}
