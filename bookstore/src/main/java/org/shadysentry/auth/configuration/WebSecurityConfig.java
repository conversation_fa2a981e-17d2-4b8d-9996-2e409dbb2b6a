package org.shadysentry.auth.configuration;

import jakarta.servlet.Filter;
import jakarta.servlet.http.<PERSON>ie;
import jakarta.servlet.http.HttpServletResponse;
import org.shadysentry.auth.filter.JwtAuthenticationFilter;
import org.shadysentry.auth.model.Role;
import org.shadysentry.auth.service.AuthService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler;
import org.springframework.security.access.expression.method.MethodSecurityExpressionHandler;
import org.springframework.security.access.hierarchicalroles.RoleHierarchy;
import org.springframework.security.access.hierarchicalroles.RoleHierarchyImpl;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.logout.LogoutFilter;
import org.springframework.security.web.servlet.util.matcher.PathPatternRequestMatcher;
import org.springframework.web.cors.CorsConfiguration;

import java.util.List;

@Configuration
@EnableWebSecurity(debug = true)
@EnableMethodSecurity
public class WebSecurityConfig {
    public static final String ROOT_PAGE = "/";
    public static final String ACTUATOR = "/actuator/**";
    public static final String SWAGGER_UI = "/swagger-ui/**";
    public static final String SWAGGER_UI_HTML = "/swagger-ui**";
    public static final String API_DOCS = "/v3/api-docs/**";
    public static String LOGIN_URL_MATCHER;
    private static String LOG_OUT_URL_MATCHER;

    private final AuthService authService;
    private final UserDetailsService userDetailsService;
    private final PasswordEncoder passwordEncoder;


    public WebSecurityConfig(AuthService authService, UserDetailsService userDetailsService, PasswordEncoder passwordEncoder) {
        this.authService = authService;
        this.userDetailsService = userDetailsService;
        this.passwordEncoder = passwordEncoder;

        LOGIN_URL_MATCHER = ApiConfig.getApiBasePath() + "/auth/login";
        LOG_OUT_URL_MATCHER = ApiConfig.getApiBasePath() + "/auth/logout";
    }

    public static List<String> getPermittedToAllUrls() {
        return List.of(ACTUATOR, API_DOCS, SWAGGER_UI, LOGIN_URL_MATCHER, LOG_OUT_URL_MATCHER, SWAGGER_UI_HTML, ROOT_PAGE);
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        final Filter jwtFilter = jwtAuthenticationFilter();

        http
                .formLogin(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(auth -> auth
                        // Public endpoints
                        .requestMatchers(getPermittedToAllUrls().toArray(new String[0])).permitAll()
                        .requestMatchers(HttpMethod.POST, ApiConfig.getApiBasePath() + "/auth/register").permitAll() //todo: remove
                        .requestMatchers(HttpMethod.POST, LOGIN_URL_MATCHER).permitAll()
                        // Authentication endpoints
                        .requestMatchers(ApiConfig.getApiBasePath() + "/auth/**").authenticated()
                        // All other API endpoints require authentication
                        .requestMatchers(ApiConfig.getApiBasePath() + "/books/**").authenticated()
                        // Deny any other request
                        .anyRequest().denyAll()
                )
                .logout(logout -> {
                    logout
                            .logoutRequestMatcher(PathPatternRequestMatcher.
                                    withDefaults().matcher(HttpMethod.POST, LOG_OUT_URL_MATCHER))
                            .logoutSuccessHandler((request, response, authentication) -> {
                                response.setStatus(HttpStatus.NO_CONTENT.value());
                                final Cookie cookie = new Cookie(AuthConstants.TOKEN_COOKIE_NAME, null);
                                cookie.setMaxAge(0);
                                response.addCookie(cookie);
                            });
                })
                .addFilterBefore(jwtFilter, LogoutFilter.class)
                .csrf((csrf) -> {
                            try {
                                csrf.disable()
                                        .sessionManagement((sessionManagement) -> sessionManagement
                                                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                                        ).oauth2ResourceServer((oauth2) -> oauth2.jwt(Customizer.withDefaults()));
                            } catch (Exception e) {
                                throw new AuthenticationException("Spring Security Config Issue", e) {
                                };
                            }
                        }
                )
                .cors(httpSecurityCorsConfigurer ->
                        httpSecurityCorsConfigurer.configurationSource(request ->
                                new CorsConfiguration().applyPermitDefaultValues())) //todo create adequat separate  config
                .authenticationManager(authenticationManager())
                .exceptionHandling(handler -> handler
                        .authenticationEntryPoint((request, response, authException) -> {
                            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                        })

                );

        return http.build();
    }

    @Bean
    public AuthenticationManager authenticationManager() {
        DaoAuthenticationProvider authenticationProvider = new DaoAuthenticationProvider(userDetailsService);
        authenticationProvider.setPasswordEncoder(passwordEncoder);

        ProviderManager providerManager = new ProviderManager(authenticationProvider);
        providerManager.setEraseCredentialsAfterAuthentication(true);
        return providerManager;
    }

    @Bean
    public MethodSecurityExpressionHandler methodSecurityExpressionHandler() {
        DefaultMethodSecurityExpressionHandler expressionHandler = new DefaultMethodSecurityExpressionHandler();
        expressionHandler.setRoleHierarchy(roleHierarchy());
        return expressionHandler;
    }

    @Bean
    public RoleHierarchy roleHierarchy() {
        return RoleHierarchyImpl.withDefaultRolePrefix()
                .role(Role.BACK_OFFICE_ADMIN.name())
                .implies(Role.SALES_MANAGER.name(), Role.END_USER.name())
                .build();
    }

    private JwtAuthenticationFilter jwtAuthenticationFilter() {
        return new JwtAuthenticationFilter(authService, userDetailsService);
    }

}
