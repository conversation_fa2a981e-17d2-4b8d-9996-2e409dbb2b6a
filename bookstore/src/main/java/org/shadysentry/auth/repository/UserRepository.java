package org.shadysentry.auth.repository;

import org.bson.types.ObjectId;
import org.shadysentry.auth.model.User;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserRepository extends MongoRepository<User, ObjectId> {

    @Query("{user_name: ?0}")
    Optional<User> findByUsername(String username);

    @Query("{email: ?0}")
    Optional<User> findByEmail(String email);

}
