package org.shadysentry;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

@Component
@Validated
@Getter
@Setter
@ConfigurationProperties(prefix = "api")
public class AuthProperties {
    @NotNull
    private String commonPath;
    @NotNull
    private String apiVersion;

    private JwtSettings jwtSettings = new JwtSettings();

    @RequiredArgsConstructor
    @Getter
    @Setter
    private class JwtSettings {
        private String secretKey;
        private int expiration;
    }
}
