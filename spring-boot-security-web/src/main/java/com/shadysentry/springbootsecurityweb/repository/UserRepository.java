package com.shadysentry.springbootsecurityweb.repository;

import com.shadysentry.springbootsecurityweb.entity.UserEntity;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface UserRepository extends JpaRepository<UserEntity, String> {

  Optional<UserEntity> findByUsername(String username);

  @Query(
      """
      SELECT CASE WHEN COUNT(u) > 0 THEN true ELSE false END FROM UserEntity u JOIN u.roles r
          WHERE r = com.shadysentry.springbootsecurityweb.common.Role.ROLE_ADMIN
      """
  )
  boolean isAnyAdminExist();
}
