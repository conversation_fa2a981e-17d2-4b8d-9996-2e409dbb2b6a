package com.shadysentry.springbootsecurityweb.entity;

import com.shadysentry.springbootsecurityweb.common.Role;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.UuidGenerator;

import java.time.ZonedDateTime;
import java.util.Set;

@Data
@Entity
@Table(name = "users")
public class UserEntity {

    @Id
    @UuidGenerator
    private String id;

    @Column(unique = true)
    private String username;

    private String passwordHash;

    private String firstName;

    private String lastName;

    @Enumerated(EnumType.STRING)
    @Column(name = "roles")
    @ElementCollection(fetch = FetchType.EAGER, targetClass = Role.class)
    private Set<Role> roles;

    private Boolean active;

    private ZonedDateTime createdDate;

    private ZonedDateTime updatedDate;

    @PrePersist
    public void onPrePersist() {

        createdDate = ZonedDateTime.now();
        updatedDate = ZonedDateTime.now();
    }

    @PreUpdate
    public void onPreUpdate() {

        updatedDate = ZonedDateTime.now();
    }
}
