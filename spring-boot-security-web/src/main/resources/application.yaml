spring:
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: update
      format_sql: true
    show-sql: true
    properties:
      hibernate:
        format_sql: true
#        physical_naming_strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
        jdbc:
          use_scrollable_resultset: false
    database: postgresql
  naming:
#    physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl # This is default
#    implicit-strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyJpaCompliantImpl # This is default
    # The key property to quote identifiers
    use-jdbc-metadata-defaults: false
  datasource:
    url: *********************************************************
#    username: springuser
    username: root
    password: root
    driver-class-name: org.postgresql.Driver

springdoc:
  swagger-ui:
    path=/swagger-ui.html

admin.default:
  username: admin
  password: admin
server:
  port: 0