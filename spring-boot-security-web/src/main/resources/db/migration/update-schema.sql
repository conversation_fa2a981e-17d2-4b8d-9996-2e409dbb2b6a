CREATE TABLE "users"
(
    id            VARCHAR(255) NOT NULL,
    username      <PERSON><PERSON><PERSON><PERSON>(255),
    password_hash VARCHA<PERSON>(255),
    first_name    <PERSON><PERSON><PERSON><PERSON>(255),
    last_name     <PERSON><PERSON><PERSON><PERSON>(255),
    active        <PERSON>O<PERSON><PERSON><PERSON>,
    created_date  TIMESTAMP WITHOUT TIME ZONE,
    updated_date  <PERSON>IM<PERSON><PERSON><PERSON> WITHOUT TIME ZONE,
    CONSTRAINT pk_user PRIMARY KEY (id)
);

CREATE TABLE user_roles (
    user_id VARCHAR(255) NOT NULL,
    roles VARCHAR(255)
);

ALTER TABLE "users"
    ADD CONSTRAINT uc_user_username UNIQUE (username);

ALTER TABLE user_roles
    ADD CONSTRAINT fk_user_roles_on_user FOREIGN KEY (user_id) REFERENCES "users" (id);
