plugins {
    id 'java'
    id 'org.springframework.boot' version '3.3.3'
    id 'io.spring.dependency-management' version '1.1.5'
    id 'com.diffplug.spotless' version '6.25.0'
}

group = 'com.shadysentry'
version = '1.0.0'

def mapStructVersion = '1.5.5.Final'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-freemarker'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation "org.mapstruct:mapstruct:$mapStructVersion"
    implementation 'org.apache.commons:commons-lang3:3.14.0'
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.5.0'
    implementation 'org.postgresql:postgresql'
    compileOnly 'org.projectlombok:lombok'

//    runtimeOnly 'com.mysql:mysql-connector-j'

    annotationProcessor 'org.projectlombok:lombok'
    annotationProcessor "org.mapstruct:mapstruct-processor:$mapStructVersion"

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
}

tasks.named('test') {
    useJUnitPlatform()
}

//spotless {
//    java {
//        importOrder()
//        googleJavaFormat()
//        removeUnusedImports()
//        trimTrailingWhitespace()
//        endWithNewline()
//    }
//}