package org.shadysentry;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Hello world!
 */
public class CharacterFrequency {
    public static void main(String[] args) {
        //given string of chars
        String inputString = "";
        //todo: count char frequency. Print if char even or odd
        Map<String, Long> charFrequencyMap = Arrays.stream(inputString.split(""))
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        charFrequencyMap.forEach((key, value) -> {
            System.out.println();
        });

    }
}